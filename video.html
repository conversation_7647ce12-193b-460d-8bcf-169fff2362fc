<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>HLS视频流播放器</title>
    <!-- 引入HLS.js库以增强浏览器对HLS的支持 -->
    <script src="https://cdn.jsdelivr.net/npm/hls.js@1.4.12/dist/hls.min.js"></script>
    <style>
      body {
        margin: 0;
        padding: 20px;
        background-color: #f0f0f0;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        min-height: 100vh;
        box-sizing: border-box;
        font-family: 'Segoe UI', Arial, sans-serif;
      }
      .player-container {
        width: 100%;
        max-width: 1200px;
        background-color: #000;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
      }
      #video-player {
        width: 100%;
        height: auto;
        aspect-ratio: 16/9;
        background-color: #000;
      }
      .controls {
        display: flex;
        align-items: center;
        padding: 12px;
        background-color: rgba(0, 0, 0, 0.8);
        color: #fff;
      }
      .control-btn {
        background: none;
        border: none;
        color: #fff;
        font-size: 18px;
        cursor: pointer;
        margin-right: 18px;
        padding: 6px;
        transition: transform 0.2s;
      }
      .control-btn:hover {
        transform: scale(1.1);
      }
      .progress-container {
        flex-grow: 1;
        height: 6px;
        background-color: rgba(255, 255, 255, 0.2);
        border-radius: 3px;
        cursor: pointer;
        position: relative;
      }
      .progress-bar {
        height: 100%;
        background-color: #ff3e3e;
        border-radius: 3px;
        width: 0%;
        transition: width 0.1s ease;
      }
      .time-display {
        margin-left: 18px;
        font-size: 14px;
        color: rgba(255, 255, 255, 0.9);
      }
      .volume-control {
        display: flex;
        align-items: center;
        margin: 0 15px;
      }
      .volume-icon {
        margin-right: 8px;
      }
      .volume-slider {
        width: 80px;
        cursor: pointer;
        accent-color: #ff3e3e;
      }
      .fullscreen-btn {
        margin-left: 10px;
      }
      .quality-selector {
        margin-left: 15px;
        padding: 5px 10px;
        background-color: rgba(255, 255, 255, 0.1);
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
      }
      .status-panel {
        margin-top: 20px;
        color: #555;
        text-align: center;
        max-width: 800px;
      }
      .status-message {
        font-size: 15px;
        margin-bottom: 8px;
      }
      .error-message {
        color: #e53e3e;
        font-size: 14px;
        display: none;
      }
    </style>
  </head>
  <body>
    <div class="player-container">
      <video id="video-player" controls></video>
      <div class="controls">
        <button class="control-btn" id="play-pause-btn">▶ 播放</button>
        <div class="volume-control">
          <span class="volume-icon" id="volume-icon">🔊</span>
          <input type="range" min="0" max="1" step="0.05" value="1" class="volume-slider" id="volume-slider" />
        </div>
        <div class="progress-container" id="progress-container">
          <div class="progress-bar" id="progress-bar"></div>
        </div>
        <span class="time-display" id="time-display">00:00 / 加载中</span>
        <select class="quality-selector" id="quality-selector" disabled>
          <option value="">选择清晰度</option>
        </select>
        <button class="control-btn fullscreen-btn" id="fullscreen-btn">⛶ 全屏</button>
      </div>
    </div>
    <div class="status-panel">
      <div class="status-message" id="status-message">HLS视频流播放器 | 准备连接视频源...</div>
      <div class="error-message" id="error-message"></div>
    </div>

    <script>
      document.addEventListener('DOMContentLoaded', function () {
        // 获取DOM元素
        const videoElement = document.getElementById('video-player')
        const playPauseBtn = document.getElementById('play-pause-btn')
        const progressContainer = document.getElementById('progress-container')
        const progressBar = document.getElementById('progress-bar')
        const timeDisplay = document.getElementById('time-display')
        const fullscreenBtn = document.getElementById('fullscreen-btn')
        const volumeSlider = document.getElementById('volume-slider')
        const volumeIcon = document.getElementById('volume-icon')
        const qualitySelector = document.getElementById('quality-selector')
        const statusMessage = document.getElementById('status-message')
        const errorMessage = document.getElementById('error-message')

        // HLS视频流地址 - 请将此地址替换为您的实际HLS流地址
        const hlsUrl = 'https://prdpulllive.xylink.com/prdnemo/9681c99c9088f6520198e4006b396992.m3u8?auth_key=667e91f6624c597adc462110e5ea17c7-1756267203-654580341c044074b144a96eb699feac-'

        let hls = null
        let isPlaying = false
        let qualityLevels = []

        // 初始化播放器
        function initPlayer() {
          // 重置状态
          errorMessage.style.display = 'none'
          statusMessage.textContent = '正在连接视频源...'
          qualitySelector.disabled = true
          qualitySelector.innerHTML = '<option value="">选择清晰度</option>'

          // 先销毁可能存在的实例
          if (hls) {
            hls.destroy()
            hls = null
          }

          // 检查浏览器是否原生支持HLS
          if (videoElement.canPlayType('application/vnd.apple.mpegurl')) {
            // 原生支持HLS
            videoElement.src = hlsUrl
            statusMessage.textContent = '浏览器原生支持HLS，正在加载...'
            setupVideoEvents()
          } else if (Hls.isSupported()) {
            // 使用HLS.js库
            hls = new Hls({
              maxBufferLength: 30,
              maxMaxBufferLength: 60,
              startLevel: -1, // 自动选择适合的初始清晰度
              maxBufferHole: 0.5,
              highLatencyMode: false
            })

            // 加载视频流
            hls.loadSource(hlsUrl)
            hls.attachMedia(videoElement)

            statusMessage.textContent = '使用HLS.js加载视频流...'

            // HLS事件监听
            hls.on(Hls.Events.MANIFEST_PARSED, function (event, data) {
              statusMessage.textContent = '视频流准备就绪，点击播放按钮开始'

              // 获取可用的清晰度级别
              qualityLevels = hls.levels
              if (qualityLevels && qualityLevels.length > 0) {
                qualitySelector.disabled = false

                // 添加清晰度选项
                qualityLevels.forEach((level, index) => {
                  const bitrate = Math.round(level.bitrate / 1000)
                  const option = document.createElement('option')
                  option.value = index
                  option.textContent = `${level.height}p (${bitrate}kbps)`
                  qualitySelector.appendChild(option)
                })
              }
            })

            // 错误处理
            hls.on(Hls.Events.ERROR, function (event, data) {
              console.error('HLS错误:', data)
              let errorText = ''

              switch (data.type) {
                case Hls.ErrorTypes.NETWORK_ERROR:
                  errorText = '网络错误，正在尝试重新连接...'
                  hls.startLoad() // 尝试重新加载
                  break
                case Hls.ErrorTypes.MEDIA_ERROR:
                  errorText = '媒体播放错误'
                  hls.recoverMediaError() // 尝试恢复媒体错误
                  break
                default:
                  errorText = `播放错误: ${data.details}`
                  // 无法恢复的错误，尝试重新初始化
                  setTimeout(initPlayer, 3000)
                  break
              }

              errorMessage.textContent = errorText
              errorMessage.style.display = 'block'
            })

            setupVideoEvents()
          } else {
            // 不支持HLS
            errorMessage.textContent = '您的浏览器不支持HLS视频流播放'
            errorMessage.style.display = 'block'
            statusMessage.textContent = 'HLS视频流播放器 | 不支持的浏览器'
          }
        }

        // 设置视频事件监听
        function setupVideoEvents() {
          // 视频可以播放时
          videoElement.addEventListener('canplay', function () {
            statusMessage.textContent = '视频准备就绪，点击播放按钮开始'
          })

          // 播放事件
          videoElement.addEventListener('play', function () {
            playPauseBtn.textContent = '⏸ 暂停'
            isPlaying = true
            statusMessage.textContent = '正在播放HLS视频流'
          })

          // 暂停事件
          videoElement.addEventListener('pause', function () {
            playPauseBtn.textContent = '▶ 播放'
            isPlaying = false
            statusMessage.textContent = 'HLS视频流已暂停'
          })

          // 时间更新事件
          videoElement.addEventListener('timeupdate', updateProgress)

          // 视频结束事件
          videoElement.addEventListener('ended', function () {
            playPauseBtn.textContent = '▶ 播放'
            isPlaying = false
            statusMessage.textContent = '视频播放已结束'
          })
        }

        // 更新进度条和时间显示
        function updateProgress() {
          if (videoElement.duration) {
            const percent = (videoElement.currentTime / videoElement.duration) * 100
            progressBar.style.width = percent + '%'

            // 更新时间显示
            const currentTime = formatTime(videoElement.currentTime)
            const duration = formatTime(videoElement.duration)
            timeDisplay.textContent = `${currentTime} / ${duration}`
          }
        }

        // 格式化时间（秒 -> MM:SS）
        function formatTime(seconds) {
          if (isNaN(seconds)) return '00:00'
          const minutes = Math.floor(seconds / 60)
          seconds = Math.floor(seconds % 60)
          return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
        }

        // 播放/暂停按钮事件
        playPauseBtn.addEventListener('click', function () {
          if (videoElement.paused) {
            videoElement.play().catch(error => {
              console.error('播放失败:', error)
              errorMessage.textContent = '播放失败，请检查视频源或浏览器设置'
              errorMessage.style.display = 'block'
            })
          } else {
            videoElement.pause()
          }
        })

        // 进度条点击事件
        progressContainer.addEventListener('click', function (e) {
          const rect = this.getBoundingClientRect()
          const pos = (e.clientX - rect.left) / rect.width
          const seekTime = pos * videoElement.duration
          videoElement.currentTime = seekTime
        })

        // 全屏按钮事件
        fullscreenBtn.addEventListener('click', function () {
          const container = document.querySelector('.player-container')
          if (!document.fullscreenElement) {
            container.requestFullscreen().catch(err => {
              console.error('全屏请求失败:', err)
              errorMessage.textContent = '全屏请求失败: ' + err.message
              errorMessage.style.display = 'block'
            })
            fullscreenBtn.textContent = '⮿ 退出全屏'
          } else {
            document.exitFullscreen()
            fullscreenBtn.textContent = '⛶ 全屏'
          }
        })

        // 全屏状态变化事件
        document.addEventListener('fullscreenchange', function () {
          if (!document.fullscreenElement) {
            fullscreenBtn.textContent = '⛶ 全屏'
          } else {
            fullscreenBtn.textContent = '⮿ 退出全屏'
          }
        })

        // 音量控制
        volumeSlider.addEventListener('input', function () {
          videoElement.volume = this.value

          // 更新音量图标
          if (this.value == 0) {
            volumeIcon.textContent = '🔇'
          } else if (this.value < 0.5) {
            volumeIcon.textContent = '🔈'
          } else {
            volumeIcon.textContent = '🔊'
          }
        })

        // 清晰度选择
        qualitySelector.addEventListener('change', function () {
          if (hls && this.value !== '') {
            const level = parseInt(this.value)
            hls.currentLevel = level
            statusMessage.textContent = `已切换至 ${qualityLevels[level].height}p 清晰度`
          }
        })

        // 页面关闭时清理
        window.addEventListener('beforeunload', function () {
          if (hls) {
            hls.destroy()
          }
        })

        // 初始化播放器
        initPlayer()
      })
    </script>
  </body>
</html>
