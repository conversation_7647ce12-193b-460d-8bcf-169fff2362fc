<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="maximum-scale=1.0,minimum-scale=1.0,user-scalable=0,width=device-width,initial-scale=1.0" />
    <meta name="format-detection" content="telephone=no,email=no,date=no,address=no" />
    <title>政协直播</title>
    <style type="text/css">
      [v-cloak] {
        display: none;
      }
      html,
      body,
      #app {
        width: 100%;
        height: 100%;
        margin: 0;
        padding: 0;
      }

      /* 顶部标题栏 */
      .header {
        height: 60px;
        background: #1f1f1f;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 16px;
      }

      .header-left {
        display: flex;
        align-items: center;
        gap: 8px;
        width: 75%;
      }

      .header-left-logo {
        width: 32px;
        height: 32px;
        border-radius: 50%;
      }

      .header-title {
        font-size: 14px;
        color: #fff;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .share-btn {
        display: flex;
        align-items: center;
        gap: 4px;
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 16px;
        padding: 5px 8px;
        font-size: 12px;
        color: #fff;
        text-decoration: none;
      }

      .share-icon {
        width: 16px;
        height: 16px;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" fill="white" viewBox="0 0 24 24"><path d="M18 16.08c-.76 0-1.44.3-1.96.77L8.91 12.7c.05-.23.09-.46.09-.7s-.04-.47-.09-.7l7.05-4.11c.54.5 1.25.81 2.04.81 1.66 0 3-1.34 3-3s-1.34-3-3-3-3 1.34-3 3c0 .***********.7L8.04 9.81C7.5 9.31 6.79 9 6 9c-1.66 0-3 1.34-3 3s1.34 3 3 3c.79 0 1.50-.31 2.04-.81l7.12 4.16c-.05.21-.08.43-.08.65 0 1.61 1.31 2.92 2.92 2.92s2.92-1.31 2.92-2.92-1.31-2.92-2.92-2.92z"/></svg>')
          no-repeat center;
        background-size: contain;
      }

      /* 主视频区域 */
      .video-container {
        position: relative;
        height: 222px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        overflow: hidden;
      }

      .video-bg {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
      }

      .video-content {
        position: relative;
        z-index: 2;
        text-align: center;
      }

      .live-graphic {
        width: 120px;
        height: 80px;
        margin: 0 auto 20px;
        background: linear-gradient(45deg, #4f46e5, #7c3aed);
        border-radius: 12px;
        position: relative;
        transform: perspective(500px) rotateX(15deg) rotateY(-15deg);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
      }

      .live-graphic::before {
        content: 'LIVE';
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        color: white;
        font-weight: bold;
        font-size: 14px;
      }

      .countdown-info {
        color: rgba(255, 255, 255, 0.8);
        font-size: 12px;
        margin-bottom: 8px;
      }

      .countdown {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
        font-size: 24px;
        font-weight: bold;
        color: #fff;
      }

      .countdown-number {
        background: rgba(0, 0, 0, 0.3);
        padding: 4px 8px;
        border-radius: 4px;
        min-width: 40px;
        text-align: center;
      }

      .countdown-separator {
        color: rgba(255, 255, 255, 0.6);
      }

      /* 直播进行中状态 */
      .live-playing {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100%;
      }

      .play-button {
        width: 80px;
        height: 80px;
        background: rgba(255, 255, 255, 0.9);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 20px;
        cursor: pointer;
        transition: all 0.3s ease;
      }

      .play-button:hover {
        background: rgba(255, 255, 255, 1);
        transform: scale(1.1);
      }

      .play-icon {
        width: 0;
        height: 0;
        border-left: 25px solid #333;
        border-top: 15px solid transparent;
        border-bottom: 15px solid transparent;
        margin-left: 5px;
      }

      .live-status {
        color: rgba(255, 255, 255, 0.8);
        font-size: 16px;
        font-weight: 500;
      }

      /* 直播结束状态 */
      .live-ended-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(31, 31, 31, 0.9);
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        z-index: 10;
        backdrop-filter: blur(2px);
      }

      .ended-title {
        color: #fff;
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 20px;
      }

      .replay-button {
        background: #3b82f6;
        color: white;
        border: none;
        border-radius: 25px;
        padding: 12px 24px;
        font-size: 16px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;
      }

      .replay-button:hover {
        background: #2563eb;
        transform: translateY(-2px);
      }

      .ended-status {
        color: rgba(255, 255, 255, 0.6);
        font-size: 14px;
        margin-top: 20px;
      }

      /* 测试按钮样式 */
      .test-controls {
        position: fixed;
        bottom: 20px;
        right: 20px;
        display: flex;
        flex-direction: column;
        gap: 8px;
        z-index: 1000;
      }

      .test-btn {
        background: rgba(59, 130, 246, 0.8);
        color: white;
        border: none;
        border-radius: 6px;
        padding: 8px 12px;
        font-size: 12px;
        cursor: pointer;
        transition: all 0.3s ease;
      }

      .test-btn:hover {
        background: rgba(59, 130, 246, 1);
      }

      .test-btn.active {
        background: rgba(34, 197, 94, 0.8);
      }

      /* 状态区域样式 */
      .status-area {
        background: #1f1f1f;
        padding: 10px 20px;
        border: 1px solid #000000;
      }

      .status-content {
        display: flex;
        align-items: center;
        justify-content: center;
        min-height: 43px;
      }

      .status-content.left-align {
        justify-content: flex-start;
      }

      .status-countdown {
        text-align: center;
      }

      .status-countdown-info {
        color: rgba(255, 255, 255, 0.6);
        font-size: 14px;
        margin-bottom: 8px;
      }

      .status-countdown-time {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
        font-size: 24px;
        font-weight: bold;
        color: #fff;
      }

      .status-countdown-number {
        padding: 4px 8px;
        min-width: 40px;
        text-align: center;
      }

      .status-countdown-separator {
        color: rgba(255, 255, 255, 0.6);
        font-size: 16px;
      }

      .status-text {
        color: rgba(255, 255, 255, 0.8);
        font-size: 16px;
        font-weight: 500;
      }

      .status-live {
        color: #999999;
      }

      .status-ended {
        color: #999999;
      }

      /* 标签栏 */
      .tabs {
        display: flex;
        background: #1f1f1f;
      }

      .tab {
        flex: 1;
        padding: 16px;
        text-align: center;
        font-size: 16px;
        color: rgba(255, 255, 255, 0.6);
        cursor: pointer;
        position: relative;
        transition: color 0.3s;
      }

      .tab.active {
        color: #fff;
      }

      .tab.active::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 40px;
        height: 2px;
        background: #3b82f6;
        border-radius: 1px;
      }

      /* 内容区域 */
      .content {
        padding: 20px 16px;
        background: #1f1f1f;
      }

      .live-title {
        font-size: 18px;
        font-weight: 600;
        line-height: 1.4;
        margin-bottom: 16px;
        color: #fff;
      }

      .live-time {
        color: rgba(255, 255, 255, 0.6);
        font-size: 14px;
        margin-bottom: 20px;
      }

      .live-description {
        color: rgba(255, 255, 255, 0.8);
        font-size: 14px;
        line-height: 1.6;
        text-align: justify;
      }

      /* 响应式设计 */
      @media (max-width: 375px) {
        .header-title {
          max-width: 150px;
        }

        .countdown {
          font-size: 20px;
        }

        .live-title {
          font-size: 16px;
        }
      }
    </style>
  </head>

  <body>
    <!-- 顶部标题栏 -->
    <header class="header">
      <div class="header-left">
        <img src="https://xaszzx.xa-cppcc.gov.cn:8131/lzt/pageImg/open/logo?v=5" alt="" class="header-left-logo" />
        <div class="header-title">"把握新形势新机遇 促进我市招商引资工作提质增效"</div>
      </div>
      <a href="#" class="share-btn">
        <div class="share-icon"></div>
        分享直播
      </a>
    </header>

    <!-- 主视频区域 -->
    <div class="video-container">
      <div class="video-bg"></div>
      <div class="video-content">
        <div class="live-graphic"></div>
        <div class="countdown-info">距离 08/25 16:30 直播开始还有</div>
        <div class="countdown">
          <span class="countdown-number">44</span>
          <span class="countdown-separator">分</span>
          <span class="countdown-number">20</span>
          <span class="countdown-separator">秒</span>
        </div>
      </div>
    </div>

    <!-- 状态区域 -->
    <div class="status-area">
      <div class="status-content" id="statusContent">
        <div class="status-countdown">
          <div class="status-countdown-info">距离 08/25 16:30 直播开始还有</div>
          <div class="status-countdown-time">
            <span class="status-countdown-number">44</span>
            <span class="status-countdown-separator">分</span>
            <span class="status-countdown-number">20</span>
            <span class="status-countdown-separator">秒</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 标签栏 -->
    <div class="tabs">
      <div class="tab active">直播详情</div>
      <div class="tab">互动</div>
    </div>

    <!-- 内容区域 -->
    <div class="content">
      <h2 class="live-title">"把握新形势新机遇 促进我市招商引资工作提质增效"专题协商会</h2>
      <div class="live-time">时间：2025/08/08 09:30-12:00</div>
      <div class="live-description">
        按照2025年度市委政治协商计划和市政协重点工作安排，今天上午9点半，市政协围绕"把握新形势新机遇，促进我市招商引资工作提质增效"召开专题协商会。自5月份以来，社会法制和民族宗教委员会通过召开情况通报会，深入开展调研，组织专题座谈会，广泛征集意见建议，进行了大量细致的准备工作。今天邀请了10家市级相关部门、部分市级民主党派、工商联和部分市政协委员进行协商交流。
      </div>
    </div>

    <!-- 测试控制按钮 -->
    <div class="test-controls">
      <button class="test-btn active" onclick="setTestStatus('not_started')">未开始</button>
      <button class="test-btn" onclick="setTestStatus('playing')">进行中</button>
      <button class="test-btn" onclick="setTestStatus('ended')">已结束</button>
    </div>

    <script>
      // 配置
      const CONFIG = {
        targetTime: new Date('2025-08-25 16:30:00').getTime(),
        endTime: new Date('2025-08-35 18:30:00').getTime()
      }

      // 当前状态
      let currentStatus = 'not_started' // not_started, playing, ended
      let isTestMode = false

      // 主更新函数
      function updateLiveStatus() {
        const now = new Date().getTime()

        // 自动判断状态（非测试模式）
        if (!isTestMode) {
          if (now < CONFIG.targetTime) {
            currentStatus = 'not_started'
          } else if (now < CONFIG.endTime) {
            currentStatus = 'playing'
          } else {
            currentStatus = 'ended'
          }
        }

        // 更新显示
        updateDisplay(now)
      }

      // 更新显示内容
      function updateDisplay(now) {
        const videoContent = document.querySelector('.video-content')
        const videoContainer = document.querySelector('.video-container')
        const statusContent = document.querySelector('#statusContent')

        // 清理遮罩层
        const existingOverlay = videoContainer.querySelector('.live-ended-overlay')
        if (existingOverlay) existingOverlay.remove()

        if (currentStatus === 'not_started') {
          showNotStartedStatus(videoContent, statusContent, now)
        } else if (currentStatus === 'playing') {
          showPlayingStatus(videoContent, statusContent)
        } else {
          showEndedStatus(videoContent, videoContainer, statusContent)
        }
      }

      // 显示未开始状态
      function showNotStartedStatus(videoContent, statusContent, now) {
        // 视频区域显示3D图形
        videoContent.innerHTML = `<div class="live-graphic"></div>`

        // 状态区域显示倒计时
        statusContent.className = 'status-content'

        // 计算倒计时
        const difference = CONFIG.targetTime - now
        let timeDisplay = ''

        if (isTestMode) {
          // 测试模式：显示动态倒计时（从44分20秒开始递减）
          const testSeconds = Math.floor((Date.now() / 1000) % 3600) // 每小时循环
          const minutes = Math.floor((2660 - testSeconds) / 60) // 从44分20秒开始
          const seconds = (2660 - testSeconds) % 60
          timeDisplay = `
          <span class="status-countdown-number">${minutes.toString().padStart(2, '0')}</span>
          <span class="status-countdown-separator">分</span>
          <span class="status-countdown-number">${seconds.toString().padStart(2, '0')}</span>
          <span class="status-countdown-separator">秒</span>
        `
        } else if (difference > 0) {
          // 真实倒计时
          const hours = Math.floor(difference / (1000 * 60 * 60))
          const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60))
          const seconds = Math.floor((difference % (1000 * 60)) / 1000)
          timeDisplay = `
          ${hours > 0 ? `<span class="status-countdown-number">${hours.toString().padStart(2, '0')}</span><span class="status-countdown-separator">时</span>` : ''}
          <span class="status-countdown-number">${minutes.toString().padStart(2, '0')}</span>
          <span class="status-countdown-separator">分</span>
          <span class="status-countdown-number">${seconds.toString().padStart(2, '0')}</span>
          <span class="status-countdown-separator">秒</span>
        `
        }

        statusContent.innerHTML = `
        <div class="status-countdown">
          <div class="status-countdown-info">距离 08/25 16:30 直播开始还有</div>
          <div class="status-countdown-time">${timeDisplay}</div>
        </div>
      `
      }

      // 显示直播进行中状态
      function showPlayingStatus(videoContent, statusContent) {
        videoContent.innerHTML = `
        <div class="live-playing">
          <div class="play-button" onclick="playLive()">
            <div class="play-icon"></div>
          </div>
        </div>
      `

        statusContent.className = 'status-content left-align'
        statusContent.innerHTML = `<div class="status-text status-live">直播进行中</div>`
      }

      // 显示直播结束状态
      function showEndedStatus(videoContent, videoContainer, statusContent) {
        videoContent.innerHTML = `<div class="live-graphic"></div>`

        // 添加遮罩层
        const overlay = document.createElement('div')
        overlay.className = 'live-ended-overlay'
        overlay.innerHTML = `
        <div class="ended-title">直播已结束</div>
        <button class="replay-button" onclick="watchReplay()">观看回放</button>
      `
        videoContainer.appendChild(overlay)

        statusContent.className = 'status-content left-align'
        statusContent.innerHTML = `<div class="status-text status-ended">直播已结束</div>`
      }

      // 交互函数
      function playLive() {
        alert('开始播放直播...')
      }

      function watchReplay() {
        alert('开始播放回放...')
      }

      // 测试控制
      function setTestStatus(status) {
        isTestMode = true
        currentStatus = status

        // 更新按钮状态
        document.querySelectorAll('.test-btn').forEach(btn => btn.classList.remove('active'))
        event.target.classList.add('active')

        updateLiveStatus()
      }

      // 初始化
      setInterval(updateLiveStatus, 1000)
      updateLiveStatus()

      // 标签切换功能
      document.querySelectorAll('.tab').forEach(tab => {
        tab.addEventListener('click', function () {
          document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'))
          this.classList.add('active')

          // 这里可以添加切换内容的逻辑
          if (this.textContent === '互动') {
            document.querySelector('.content').innerHTML = `
                        <div style="text-align: center; padding: 40px 0; color: rgba(255,255,255,0.6);">
                            <div style="font-size: 48px; margin-bottom: 16px;">💬</div>
                            <div>互动功能开发中...</div>
                        </div>
                    `
          } else {
            location.reload() // 简单的重新加载来显示原始内容
          }
        })
      })

      // 分享功能
      document.querySelector('.share-btn').addEventListener('click', function (e) {
        e.preventDefault()
        // 这里可以添加分享逻辑
        alert('分享功能开发中...')
      })
    </script>
  </body>
</html>
